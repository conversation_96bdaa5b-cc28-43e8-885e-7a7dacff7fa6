import { Entity, EntityDamageSource, system, world } from "@minecraft/server";
import { handleLootMechanics } from "./lootMechanics";
import { zombieBruteMechanics } from "../bosses/necromancer/minions/zombie_brute/index";
import { piglinBruteMechanics } from "../bosses/piglin_champion/minions/piglin_brute/index";
import { piglinMarauderMechanics } from "../bosses/piglin_champion/minions/piglin_marauder/index";
import { grimhowlMechanics } from "../bosses/grimhowl/index";
import { piglinChampionMechanics } from "../bosses/piglin_champion/index";

export const customEntityIds = new Set<string>(["ptd_dbb:winged_zombie", "ptd_dbb:zombie_brute", "ptd_dbb:skeleton_soul", "ptd_dbb:piglin_brute", "ptd_dbb:piglin_marauder"]);

// Global entity event listeners
world.afterEvents.entityHurt.subscribe((event: { damage: number; damageSource: EntityDamageSource; hurtEntity: Entity }) => {
  if (event.hurtEntity.typeId !== "ptd_dbb:grimhowl") return;
  grimhowlMechanics(event, "hurt");
});

world.afterEvents.entityLoad.subscribe((event: { entity: Entity }) => {
  if (event.entity.typeId !== "ptd_dbb:grimhowl") return;
  grimhowlMechanics(event, "load");
});

world.afterEvents.dataDrivenEntityTrigger.subscribe(async (event) => {
  const entity = event.entity;
  const entityTypeId = entity.typeId;
  const eventId = event.eventId;

  // Wait 1 tick so the selected attack is set properly
  await system.waitTicks(1);

  // Handle attack logic
  if (eventId === "ptd_dbb:attack") {
    switch (entityTypeId) {
      case "ptd_dbb:zombie_brute":
        zombieBruteMechanics(entity);
        break;
      case "ptd_dbb:piglin_brute":
        piglinBruteMechanics(entity);
        break;
      case "ptd_dbb:piglin_marauder":
        piglinMarauderMechanics(entity);
        break;
      case "ptd_dbb:piglin_champion":
        piglinChampionMechanics(entity);
        break;
      default:
        break;
    }
  }

  // Handle spawning tick logic
  if (eventId === "ptd_dbb:spawning_tick") {
    if (entityTypeId === "ptd_dbb:piglin_champion") {
      // Handle spawning effects at specific ticks
      const spawningTicks = entity.getProperty("ptd_dbb:spawning_ticks") as number;

      // Apply shockwave effect on tick 6
      if (spawningTicks === 6) {
        // Import shockwave and camera shake functions
        const { shockwave } = await import("../bosses/general_attacks/shockwave");
        const { cameraShake } = await import("../bosses/general_effects/camerashake");

        // Apply shockwave with radius 8, power 1.5, damage 8, excluding piglin_champion family
        shockwave(entity, 5, 1.5, 8, ["piglin_champion"]);
        // Apply camera shake effect
        cameraShake(entity, 32, 0.02, 0.5, 0.5);
        // Play a particle effect at the piglin's location
        entity.dimension.spawnParticle("minecraft:large_explosion", entity.location);
      }
    }
  }



  // Handle death logic
  if (eventId === "ptd_dbb:dead") {
    switch (entityTypeId) {
      case "ptd_dbb:skeleton_soul":
        deathMechanics(entity, 10);
        break;
      case "ptd_dbb:winged_zombie":
        deathMechanics(entity, 36);
        break;
      case "ptd_dbb:zombie_brute":
        deathMechanics(entity, 36);
        break;
      case "ptd_dbb:piglin_brute":
        deathMechanics(entity, 36);
        break;
      case "ptd_dbb:piglin_marauder":
        deathMechanics(entity, 36);
        break;
      case "ptd_dbb:piglin_champion":
        deathMechanics(entity, 100);
        break;
      default:
        break;
    }
  }
});

function deathMechanics(entity: Entity, ticks: number) {
  if (!entity) return;

  const deathProp = entity.getProperty("ptd_dbb:dead") as boolean;

  if (deathProp) {
    let currentTick = 0;
    let deathInterval: number;

    // Create an interval that runs death mechanics every tick for the specified duration
    deathInterval = system.runInterval(() => {
      try {
        // Check if entity is still valid
        if (!entity) {
          if (deathInterval !== undefined) {
            system.clearRun(deathInterval);
          }
          return;
        }

        // Execute death mechanics for this tick
        handleLootMechanics(entity, currentTick + 1);

        currentTick++;

        // Clear interval when we've reached the specified duration
        if (currentTick >= ticks) {
          if (deathInterval !== undefined) {
            system.clearRun(deathInterval);
          }
        }
      } catch (error) {
        // Clear interval on any error to prevent memory leaks
        if (deathInterval !== undefined) {
          system.clearRun(deathInterval);
        }
        console.warn(`Error in death mechanics interval: ${error}`);
      }
    }, 1); // Run every tick
  }
  return;
}
