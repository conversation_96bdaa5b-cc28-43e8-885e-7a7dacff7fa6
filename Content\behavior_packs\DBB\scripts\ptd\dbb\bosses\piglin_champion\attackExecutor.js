import { system } from "@minecraft/server";
import { getTarget } from "../general_mechanics/targetUtils";
import { executeHorizontalAttack } from "./attacks/horizontal";
import { executeVerticalAttack } from "./attacks/vertical";
import { executeFootStompAttack } from "./attacks/foot_stomp";
import { executeSpinSlamAttack } from "./attacks/spin_slam";
import { executeBodySlamAttack } from "./attacks/body_slam";
import { executeUpchuckAttack } from "./attacks/upchuck";
import { executeChargingAttack, startContinuousChargingDamage } from "./attacks/charging";
import { executeSummoningChantAttack } from "./attacks/summoning_chant";
import { executeHealingAbility } from "./abilities/healing";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";
/**
 * Attack timing points in ticks
 * When the damage and effects should be applied
 */
export const ATTACK_TIMINGS = {
    horizontal: 38,
    vertical: 44,
    foot_stomp: 24,
    spin_slam_phase1: 83,
    spin_slam_phase2: 124,
    body_slam: 63,
    upchuck: 55,
    charging_continuous: 46, // Start continuous damage during charge movement
    charging: 96, // Final impact damage
    healing_phase1: 43,
    healing_phase2: 130,
    summoning_chant: 40,
    stunned_standing_phase1: 30, // End of damage_to_stunned
    stunned_standing_phase2: 150, // End of stunned_standing (30 + 120)
    stunned_sitting_phase1: 30, // End of damage_to_stunned
    stunned_sitting_phase2: 150 // End of stunned_sitting (30 + 120)
};
/**
 * Attack durations in ticks
 * How long each attack animation lasts
 */
export const ATTACK_DURATIONS = {
    horizontal: 70,
    vertical: 80,
    foot_stomp: 50,
    spin_slam: 170,
    body_slam: 100,
    upchuck: 170,
    charging: 310,
    healing: 158,
    summoning_chant: 80,
    stunned_standing: 240,
    stunned_sitting: 333
};
/**
 * Executes an attack using runTimeout-based timing instead of timer-based system
 * @param piglinChampion The piglin champion entity
 * @param attack The attack type to execute
 */
export function executeAttack(piglinChampion, attack) {
    try {
        // Get target for attacks that need it
        const target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
        // Schedule attack events based on attack type
        switch (attack) {
            case "horizontal":
                scheduleHorizontalAttack(piglinChampion);
                break;
            case "vertical":
                scheduleVerticalAttack(piglinChampion, target);
                break;
            case "foot_stomp":
                scheduleFootStompAttack(piglinChampion);
                break;
            case "spin_slam":
                scheduleSpinSlamAttack(piglinChampion);
                break;
            case "body_slam":
                scheduleBodySlamAttack(piglinChampion);
                break;
            case "upchuck":
                scheduleUpchuckAttack(piglinChampion);
                break;
            case "charging":
                scheduleChargingAttack(piglinChampion);
                break;
            case "healing":
                scheduleHealingAbility(piglinChampion);
                break;
            case "summoning_chant":
                scheduleSummoningChantAttack(piglinChampion, target);
                break;
            case "stunned_standing":
                scheduleStunnedStanding(piglinChampion);
                break;
            case "stunned_sitting":
                scheduleStunnedSitting(piglinChampion);
                break;
            default:
                console.warn(`Unknown attack type: ${attack}`);
                break;
        }
    }
    catch (error) {
        console.warn(`Error executing attack ${attack}: ${error}`);
    }
}
/**
 * Schedule horizontal attack events
 */
function scheduleHorizontalAttack(piglinChampion) {
    // Execute attack at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "horizontal") {
                executeHorizontalAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.horizontal);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "horizontal") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.horizontal);
}
/**
 * Schedule vertical attack events
 */
function scheduleVerticalAttack(piglinChampion, target) {
    // Execute attack at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "vertical") {
                // Get fresh target if needed
                const currentTarget = target || getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
                if (currentTarget) {
                    executeVerticalAttack(piglinChampion, currentTarget);
                }
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.vertical);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "vertical") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.vertical);
}
/**
 * Schedule foot stomp attack events
 */
function scheduleFootStompAttack(piglinChampion) {
    // Execute attack at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "foot_stomp") {
                executeFootStompAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.foot_stomp);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "foot_stomp") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.foot_stomp);
}
/**
 * Schedule spin slam attack events (two phases)
 */
function scheduleSpinSlamAttack(piglinChampion) {
    // Execute phase 1 at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                executeSpinSlamAttack(piglinChampion, 1); // Phase 1: Lift
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.spin_slam_phase1);
    // Execute phase 2 at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                executeSpinSlamAttack(piglinChampion, 2); // Phase 2: Slam
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.spin_slam_phase2);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "spin_slam") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.spin_slam);
}
/**
 * Schedule body slam attack events
 */
function scheduleBodySlamAttack(piglinChampion) {
    // Execute attack at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "body_slam") {
                executeBodySlamAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.body_slam);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "body_slam") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.body_slam);
}
/**
 * Schedule upchuck attack events
 */
function scheduleUpchuckAttack(piglinChampion) {
    // Execute attack at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "upchuck") {
                executeUpchuckAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.upchuck);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "upchuck") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.upchuck);
}
/**
 * Schedule charging attack events (complex multi-phase attack)
 */
function scheduleChargingAttack(piglinChampion) {
    // Apply speed 3 effect at the start of charge2 animation (tick 30)
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                // Apply speed 3 effect for the duration of charge_2 and charge_3 (60 ticks)
                piglinChampion.addEffect("minecraft:speed", 60, { amplifier: 3, showParticles: false });
                piglinChampion.triggerEvent("ptd_dbb:charging2"); // Increase speed in the component groups
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, 30);
    // Start continuous charging damage during charge movement
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                startContinuousChargingDamage(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.charging_continuous);
    // Apply slowness effect at the start of stunned_sitting animation (tick 90)
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                // Apply slowness for the duration of stunned_sitting and charge_4 (220 ticks)
                piglinChampion.addEffect("minecraft:slowness", 220, { amplifier: 250, showParticles: false });
                piglinChampion.triggerEvent("ptd_dbb:stun_after_charge"); // Stun the piglin - remove the melee component group temporarily
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, 90);
    // Execute final charging attack impact at the right timing
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                executeChargingAttack(piglinChampion);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.charging);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "charging") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
                // Recover from stun after charging attack - add the melee component group back
                piglinChampion.triggerEvent("ptd_dbb:recover_after_charge");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.charging);
}
/**
 * Schedule healing ability events (two phases)
 */
function scheduleHealingAbility(piglinChampion) {
    // Execute phase 1 at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                executeHealingAbility(piglinChampion, 1); // Phase 1: Healing
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.healing_phase1);
    // Execute phase 2 at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                executeHealingAbility(piglinChampion, 2); // Phase 2: Knockback
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.healing_phase2);
    // Complete healing and trigger upchuck attack
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "healing") {
                // Remove invulnerability when healing animation ends
                piglinChampion.triggerEvent("ptd_dbb:end_healing");
                // Stop all other sound effects except for upchuck sound
                const upchuckSound = ATTACK_SOUND_MAP["upchuck"];
                stopPiglinChampionSounds(piglinChampion, upchuckSound);
                piglinChampion.triggerEvent("ptd_dbb:upchuck_attack");
                // Apply slowness effect for the duration of the upchuck animation
                const upchuckDuration = ATTACK_DURATIONS.upchuck || 170; // Fallback to 170 ticks if undefined
                piglinChampion.addEffect("minecraft:slowness", upchuckDuration, { amplifier: 250, showParticles: false });
                // Schedule upchuck attack execution
                executeAttack(piglinChampion, "upchuck");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.healing);
}
/**
 * Schedule summoning chant attack events
 */
function scheduleSummoningChantAttack(piglinChampion, target) {
    // Execute attack at timing point
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "summoning_chant") {
                // Get fresh target if needed
                const currentTarget = target || getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
                // Execute summoning chant with target information
                executeSummoningChantAttack(piglinChampion, currentTarget);
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_TIMINGS.summoning_chant);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "summoning_chant") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.summoning_chant);
}
/**
 * Schedule stunned standing events
 */
function scheduleStunnedStanding(piglinChampion) {
    // Disable look_at_target animation during stunned animations
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "stunned_standing") {
                // Disable look_at_target animation
                piglinChampion.triggerEvent("ptd_dbb:stun_after_charge"); // Reuse the existing event that disables melee
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, 1);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "stunned_standing") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.stunned_standing);
}
/**
 * Schedule stunned sitting events
 */
function scheduleStunnedSitting(piglinChampion) {
    // Disable look_at_target animation during stunned animations
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "stunned_sitting") {
                // Disable look_at_target animation
                piglinChampion.triggerEvent("ptd_dbb:stun_after_charge"); // Reuse the existing event that disables melee
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, 1);
    // Reset attack when complete
    system.runTimeout(() => {
        try {
            if (piglinChampion.getProperty("ptd_dbb:attack") === "stunned_sitting") {
                stopPiglinChampionSounds(piglinChampion);
                piglinChampion.triggerEvent("ptd_dbb:reset_attack");
            }
        }
        catch (error) {
            // Entity might have been removed
        }
    }, ATTACK_DURATIONS.stunned_sitting);
}
