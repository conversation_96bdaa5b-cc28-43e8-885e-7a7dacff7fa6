import { EntityComponentTypes } from "@minecraft/server";
import { shockwave } from "../general_attacks/shockwave";
import { handleDeathMechanics } from "../general_mechanics/deathMechanics";
import { selectAttack } from "./controller";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";
import { spawnItemFountain } from "../general_mechanics/itemFountain";
import { getTarget } from "../general_mechanics/targetUtils";
import { cameraShake } from "../general_effects/camerashake";
import { executeAttack } from "./attackExecutor";
/**
 * Handles the piglin champion boss mechanics
 * @param piglinChampion The piglin champion entity
 */
export function piglinChampionMechanics(piglinChampion) {
    // Skip if entity is not valid
    try {
        if (!piglinChampion)
            return;
    }
    catch (e) {
        return;
    }
    // Get entity properties
    const isSpawning = piglinChampion.getProperty("ptd_dbb:spawning");
    const isDead = piglinChampion.getProperty("ptd_dbb:dead");
    const attack = piglinChampion.getProperty("ptd_dbb:attack");
    const stunStandingTriggered = piglinChampion.getProperty("ptd_dbb:stun_standing_triggered");
    const stunSittingTriggered = piglinChampion.getProperty("ptd_dbb:stun_sitting_triggered");
    // Cancel any ongoing attack if the entity is dead
    if (isDead && attack !== "none") {
        // Stop all sounds when the entity is dead
        stopPiglinChampionSounds(piglinChampion, "mob.ptd_dbb_piglin_champion.death");
        piglinChampion.triggerEvent("ptd_dbb:reset_attack");
    }
    // Handle death mechanics using the generalized function
    // If the entity is dead, this will handle all death-related behavior and return true
    if (handleDeathMechanics(piglinChampion, {
        // Configure death mechanics specific to the Piglin Champion
        duration: 100,
        xpOrbs: {
            count: 8,
            duration: 30,
            heightOffset: 2.25,
        },
        // No drops here as we'll use a custom event to spawn the essence fountain
        drops: [],
        deathSound: "mob.ptd_dbb_piglin_champion.death",
        // Add custom event to spawn essence fountain at the beginning of death sequence
        customEvents: [
            {
                tick: 1,
                callback: (entity) => {
                    piglinChampion.dimension.spawnParticle("ptd_dbb:pg_die1_01", entity.location);
                    // Spawn 32 essence items in a fountain-like effect
                    spawnItemFountain(entity, "ptd_dbb:piglin_champion_essence", 32, {
                        heightOffset: 2.25,
                        particleEffect: "minecraft:large_explosion",
                        soundEffect: "random.pop",
                        minVerticalStrength: 0.1,
                        maxVerticalStrength: 0.3,
                        minHorizontalStrength: 0.05,
                        maxHorizontalStrength: 0.2,
                    });
                },
            },
        ],
        // Provide the sound stopping function
        stopSoundsFn: stopPiglinChampionSounds,
    })) {
        // If death mechanics were applied, return early
        return;
    }
    if (isSpawning) {
        // Get current spawning ticks
        const currentTicks = piglinChampion.getProperty("ptd_dbb:spawning_ticks");
        // Increment spawning ticks, capped at 114 (total animation length)
        const newTicks = Math.min(currentTicks + 1, 114);
        piglinChampion.setProperty("ptd_dbb:spawning_ticks", newTicks);
        // Apply shockwave effect on tick 6
        if (currentTicks === 5 && newTicks === 6) {
            // Apply shockwave with radius 8, power 1.5, damage 8, excluding piglin_champion family
            shockwave(piglinChampion, 5, 1.5, 8, ["piglin_champion"]);
            // Apply camera shake effect
            cameraShake(piglinChampion, 32, 0.02, 0.5, 0.5);
            // Play a particle effect at the piglin's location
            piglinChampion.dimension.spawnParticle("minecraft:large_explosion", piglinChampion.location);
            // Play a sound effect
            piglinChampion.dimension.playSound("random.explode", piglinChampion.location);
        }
        // Spawn final particle effect near the end of the animation
        if (currentTicks === 48) {
            // Play the final particle effect at the piglin's location
            piglinChampion.dimension.spawnParticle("ptd_dbb:pg_spawn3_01", piglinChampion.location);
        }
    }
    else {
        // Target detection and combat mechanics
        const target = getTarget(piglinChampion, piglinChampion.location, 32, ["piglin_champion"]);
        // Get attack properties
        // We already got the attack property earlier for death check
        let attackCooldown = piglinChampion.getProperty("ptd_dbb:attack_cooldown");
        if (target) {
            // Handle attack cooldown
            if (attackCooldown > 0) {
                // Use Math.max to ensure cooldown never goes below 0
                piglinChampion.setProperty("ptd_dbb:attack_cooldown", Math.max(0, attackCooldown - 1));
            }
            // Check health for healing ability and stun mechanics
            const healthComponent = piglinChampion.getComponent(EntityComponentTypes.Health);
            const health = healthComponent?.currentValue || 0;
            const maxHealth = healthComponent?.defaultValue || 0; // Default max health if not set
            const lastHealThreshold = piglinChampion.getProperty("ptd_dbb:last_heal_threshold");
            // Calculate current health percentage threshold (0-3 for 75%, 50%, 25%, 0%)
            const currentThreshold = Math.floor((health / maxHealth) * 4);
            // Calculate health percentage for stun mechanics
            const healthPercentage = (health / maxHealth) * 100;
            // Check for stun standing at 65% health
            if (healthPercentage <= 65 &&
                !stunStandingTriggered &&
                attack !== "stunned_standing" &&
                attack !== "stunned_sitting" &&
                !isDead) {
                // Cancel any ongoing attack
                if (attack !== "none") {
                    // Stop all sounds when resetting attack
                    stopPiglinChampionSounds(piglinChampion);
                    piglinChampion.triggerEvent("ptd_dbb:reset_attack");
                }
                // Stop all other sound effects except for stunned standing sound
                const stunSound = ATTACK_SOUND_MAP["stunned_standing"];
                stopPiglinChampionSounds(piglinChampion, stunSound);
                // Trigger stunned standing
                piglinChampion.triggerEvent("ptd_dbb:stunned_standing");
                // Apply slowness effect for the duration of the stun
                piglinChampion.addEffect("minecraft:slowness", 240, { amplifier: 250, showParticles: false });
                return; // Exit early to prevent other actions this tick
            }
            // Check for stun sitting at 35% health
            if (healthPercentage <= 35 && !stunSittingTriggered && attack !== "stunned_sitting" && !isDead) {
                // Cancel any ongoing attack
                if (attack !== "none") {
                    // Stop all sounds when resetting attack
                    stopPiglinChampionSounds(piglinChampion);
                    piglinChampion.triggerEvent("ptd_dbb:reset_attack");
                }
                // Stop all other sound effects except for stunned sitting sound
                const stunSound = ATTACK_SOUND_MAP["stunned_sitting"];
                stopPiglinChampionSounds(piglinChampion, stunSound);
                // Trigger stunned sitting
                piglinChampion.triggerEvent("ptd_dbb:stunned_sitting");
                // Apply slowness effect for the duration of the stun
                piglinChampion.addEffect("minecraft:slowness", 333, { amplifier: 250, showParticles: false });
                return; // Exit early to prevent other actions this tick
            }
            // Execute attack if one was just triggered (attack property changed from "none")
            if (attack !== "none") {
                // Check if this is a newly triggered attack by seeing if we need to execute it
                // In the new system, attacks are executed immediately when triggered
                const previousAttack = piglinChampion.getProperty("ptd_dbb:previous_attack") || "none";
                // If this is a new attack (different from previous), execute it
                if (attack !== previousAttack) {
                    piglinChampion.setProperty("ptd_dbb:previous_attack", attack);
                    executeAttack(piglinChampion, attack);
                }
            }
            else {
                // Reset previous attack when no attack is active
                piglinChampion.setProperty("ptd_dbb:previous_attack", "none");
                // Select a new attack if not currently attacking and cooldown is 0
                if (attackCooldown === 0) {
                    // Check if we need to heal first
                    if (currentThreshold < lastHealThreshold) {
                        // Update last heal threshold
                        piglinChampion.setProperty("ptd_dbb:last_heal_threshold", currentThreshold);
                        // Stop all other sound effects except for healing sound
                        const healingSound = ATTACK_SOUND_MAP["healing"];
                        stopPiglinChampionSounds(piglinChampion, healingSound);
                        // Trigger healing ability
                        piglinChampion.triggerEvent("ptd_dbb:healing_ability");
                        // Apply slowness with amplifier 250 for the duration of the healing animation
                        piglinChampion.addEffect("minecraft:slowness", 158, { amplifier: 250, showParticles: false });
                    }
                    else {
                        // Otherwise select a normal attack
                        selectAttack(piglinChampion, target);
                    }
                }
            }
        }
    }
    if (attack !== "none") {
        // Stop all sounds when resetting attack
        stopPiglinChampionSounds(piglinChampion);
        piglinChampion.triggerEvent("ptd_dbb:reset_attack");
    }
}
